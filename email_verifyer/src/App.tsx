import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './App.css'
import { DashboardLayout } from './components/dashboard/layout'
import EmailValidationApp from './components/dashboard/EmailVerificationPage'
import UploadPage from './components/dashboard/UploadPage'
import CreditsPage from './components/dashboard/CreditsPage'
import Hero from './components/Hero'

function App() {
  return (
    <Router>
      <Routes>

        <Route path="/dashboard" element={<DashboardLayout />}>
          <Route path="verify" element={<EmailValidationApp />} />
          <Route path="upload" element={<UploadPage />} />
          <Route path="credits" element={<CreditsPage />} />
          <Route index element={<EmailValidationApp />} />
        </Route>
        <Route path="/" element={< Hero />} />
      </Routes>
    </Router>
  )
}

export default App
