import {  BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './App.css'

import { DashboardLayout } from './components/dashboard/layout'
import EmailValidationApp from './components/dashboard/EmailVerificationPage'
import FileUploadBox from './components/dashboard/Upload'
// import { EmailVerificationPage } from './components/dashboard/EmailVerificationPage'

function App() {
  return (
    <>

    <Router>

      

    <DashboardLayout>
      
     <Routes>
           {/* <Route path="/dashboard/verify" element={<EmailVerificationPage />}></Route> */}
           <Route path="/dashboard/verify" element={<EmailValidationApp/> }></Route>
           <Route path="/dashboard/upload" element={<FileUploadBox/> }></Route>

      </Routes>
    </DashboardLayout>
    </Router>
    </>
  )
}

export default App
