import {  BrowserRouter as Router, Routes } from 'react-router-dom'
import './App.css'

import { DashboardLayout } from './components/dashboard/layout'
// import { EmailVerificationPage } from './components/dashboard/EmailVerificationPage'

function App() {
  return (
    <>

    <Router>

      

    <DashboardLayout>
      
     <Routes>
           {/* <Route path="/dashboard/verify" element={<EmailVerificationPage />}></Route> */}
      </Routes>
    </DashboardLayout>
    </Router>
    </>
  )
}

export default App
