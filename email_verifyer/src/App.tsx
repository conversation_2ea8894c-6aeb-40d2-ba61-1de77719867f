import {  BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './App.css'


import EmailValidationApp from './components/dashboard/EmailVerificationPage'
import UploadPage from './components/dashboard/UploadPage'
import CreditsPage from './components/dashboard/CreditsPage'

// import { EmailVerificationPage } from './components/dashboard/EmailVerificationPage'

function App() {
  return (
    <>

    <Router>
      <Routes>
        <Route path="/dashboard/verify" element={<EmailValidationApp />} />
        <Route path="/dashboard/upload" element={<UploadPage />} />
        <Route path="/dashboard/credits" element={<CreditsPage />} />
        <Route path="/" element={<EmailValidationApp />} />
      </Routes>
    </Router>
    </>
  )
}

export default App
