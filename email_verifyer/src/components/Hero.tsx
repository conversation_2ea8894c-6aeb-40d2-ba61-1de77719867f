import { Mail, CheckCircle, FileText, Star, Users, Shield, Zap, ArrowRight, Check, X } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Hero = () => {
  const [email, setEmail] = useState('');
 

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Marketing Director",
      company: "TechCorp",
      avatar: "SJ",
      rating: 5,
      text: "Cut our bounce rate by 78% in the first month. The API integration was seamless and support is outstanding."
    },
    {
      name: "<PERSON>",
      role: "Email Marketing Manager",
      company: "GrowthCo",
      avatar: "MC",
      rating: 5,
      text: "Best investment we made this year. Saved us thousands in ESP costs and improved our sender reputation significantly."
    },
    {
      name: "<PERSON>",
      role: "CTO",
      company: "StartupXYZ",
      avatar: "ED",
      rating: 5,
      text: "Lightning fast validation and incredibly accurate. Their bulk processing handles our 500k+ lists without breaking a sweat."
    }
  ];

  const plans = [
    {
      name: "Starter",
      price: "$19",
      period: "/month",
      emails: "5,000",
      features: [
        "5,000 email validations/month",
        "Real-time API access",
        "Basic support",
        "99.2% accuracy",
        "CSV upload/download"
      ],
      notIncluded: [
        "Priority support",
        "Custom integrations",
        "Advanced analytics"
      ]
    },
    {
      name: "Professional",
      price: "$49",
      period: "/month",
      emails: "25,000",
      popular: true,
      features: [
        "25,000 email validations/month",
        "Real-time API access",
        "Priority support",
        "99.5% accuracy",
        "CSV/Excel upload/download",
        "Advanced analytics",
        "Custom integrations"
      ],
      notIncluded: [
        "Dedicated account manager"
      ]
    },
    {
      name: "Enterprise",
      price: "$149",
      period: "/month",
      emails: "100,000",
      features: [
        "100,000 email validations/month",
        "Real-time API access",
        "24/7 priority support",
        "99.7% accuracy",
        "All file formats",
        "Advanced analytics",
        "Custom integrations",
        "Dedicated account manager",
        "SLA guarantee"
      ],
      notIncluded: []
    }
  ];

  const navigate = useNavigate()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                <Mail className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">EmailClean</span>
            </div>
            <div className="flex items-center space-x-4">
              
              <button className="bg-green-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
                Sign Up Free
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="mb-6">
            <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium mb-6">
              <CheckCircle className="w-4 h-4 mr-2" />
              99.5% Accuracy Rate • Trusted by 10,000+ Businesses
            </div>
          </div>
                     
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Clean Your Email Lists
            <span className="block text-green-600">Boost Deliverability</span>
          </h1>
                     
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            Professional email validation service that removes invalid addresses, catches typos, and ensures your campaigns reach real inboxes. Validate single emails or process bulk lists instantly.
          </p>
                     
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-green-700 transition-all transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center">
              <Mail className="w-5 h-5 mr-2" />
              Start Validating Free
            </button>
            <button className="bg-white text-gray-700 px-8 py-4 rounded-lg font-semibold text-lg border-2 border-gray-300 hover:border-green-500 hover:text-green-600 transition-all flex items-center"
                onClick={() => navigate('/dashboard/verify')}
            >
              <FileText className="w-5 h-5 mr-2" />
              View Demo
            </button>
          </div>
                     
          <div className="flex flex-col sm:flex-row gap-8 justify-center items-center text-sm text-gray-500">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              No credit card required
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              Process 100 emails free
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              Results in seconds
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">99.5%</div>
            <div className="text-sm text-gray-600">Accuracy Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">10M+</div>
            <div className="text-sm text-gray-600">Emails Validated</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">50k+</div>
            <div className="text-sm text-gray-600">Happy Customers</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">0.3s</div>
            <div className="text-sm text-gray-600">Avg Response Time</div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Why Choose EmailClean?</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">99.5% Accuracy</h3>
              <p className="text-gray-600">Industry-leading accuracy with advanced validation algorithms and real-time verification.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Lightning Fast</h3>
              <p className="text-gray-600">Process millions of emails in minutes with our optimized infrastructure and global CDN.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Enterprise Ready</h3>
              <p className="text-gray-600">GDPR compliant with enterprise-grade security, SLA guarantees, and 24/7 support.</p>
            </div>
          </div>
        </div>

        {/* Testimonials Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">What Our Customers Say</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.role} at {testimonial.company}</div>
                  </div>
                </div>
                <div className="flex mb-3">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">"{testimonial.text}"</p>
              </div>
            ))}
          </div>
        </div>

        {/* Pricing Plans */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-4">Choose Your Plan</h2>
          <p className="text-center text-gray-600 mb-12">Start free, scale as you grow. All plans include our core validation features.</p>
          
          <div className="grid md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <div key={index} className={`bg-white rounded-xl border-2 p-8 relative ${plan.popular ? 'border-green-500 shadow-lg' : 'border-gray-200'}`}>
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </div>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{plan.name}</h3>
                  <div className="mb-2">
                    <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                    <span className="text-gray-600">{plan.period}</span>
                  </div>
                  <div className="text-sm text-gray-600">{plan.emails} emails/month</div>
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start">
                      <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{feature}</span>
                    </li>
                  ))}
                  {plan.notIncluded.map((feature, idx) => (
                    <li key={idx} className="flex items-start">
                      <X className="w-5 h-5 text-gray-300 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-400 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                  plan.popular 
                    ? 'bg-green-600 text-white hover:bg-green-700' 
                    : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                }`}>
                  Start {plan.name} Plan
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Newsletter Subscription */}
        <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-2xl p-8 mb-16 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Stay Updated</h2>
          <p className="text-green-100 mb-6 max-w-2xl mx-auto">
            Get the latest tips on email marketing, deliverability best practices, and product updates delivered to your inbox monthly.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="flex-1 px-4 py-3 rounded-lg text-white bg-green-100  placeholder-gray-500 border-0 focus:ring-2 focus:ring-green-300 outline-none"
            />
            <button className="bg-white text-green-700 px-6 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors flex items-center">
              Subscribe
              <ArrowRight className="w-4 h-4 ml-2" />
            </button>
          </div>
          <p className="text-green-200 text-sm mt-3">No spam, unsubscribe anytime</p>
        </div>

        {/* Additional Info Section */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Advanced Validation Technology</h3>
            <div className="space-y-4">
              <div className="flex items-start">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <div className="font-medium text-gray-900">Syntax & Format Check</div>
                  <div className="text-gray-600 text-sm">Validates email format and catches common typos</div>
                </div>
              </div>
              <div className="flex items-start">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <div className="font-medium text-gray-900">Domain Verification</div>
                  <div className="text-gray-600 text-sm">Checks if domain exists and accepts mail</div>
                </div>
              </div>
              <div className="flex items-start">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <div className="font-medium text-gray-900">Mailbox Verification</div>
                  <div className="text-gray-600 text-sm">Verifies if specific mailbox exists and is active</div>
                </div>
              </div>
              <div className="flex items-start">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <div className="font-medium text-gray-900">Risk Assessment</div>
                  <div className="text-gray-600 text-sm">Identifies risky, disposable, and catch-all emails</div>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Integration & Security</h3>
            <div className="space-y-4">
              <div className="flex items-start">
                <Shield className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <div className="font-medium text-gray-900">Enterprise Security</div>
                  <div className="text-gray-600 text-sm">SOC 2 compliant with end-to-end encryption</div>
                </div>
              </div>
              <div className="flex items-start">
                <Shield className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <div className="font-medium text-gray-900">GDPR Compliant</div>
                  <div className="text-gray-600 text-sm">Full compliance with data protection regulations</div>
                </div>
              </div>
              <div className="flex items-start">
                <Shield className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <div className="font-medium text-gray-900">RESTful API</div>
                  <div className="text-gray-600 text-sm">Easy integration with comprehensive documentation</div>
                </div>
              </div>
              <div className="flex items-start">
                <Shield className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <div className="font-medium text-gray-900">Webhook Support</div>
                  <div className="text-gray-600 text-sm">Real-time notifications for bulk validations</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500 border-t pt-8">
          <p>Secure, fast, and reliable email validation for your business needs</p>
          <p className="mt-2">© 2025 EmailClean. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
};

export default Hero;