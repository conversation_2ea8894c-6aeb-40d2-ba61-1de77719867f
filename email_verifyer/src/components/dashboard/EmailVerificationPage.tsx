import  { useState } from 'react';
export function EmailVerificationPage() {
  const [email, setEmail] = useState('');
  const [result, setResult] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleVerifyEmail = async () => {
    if (!email) return;
    
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setResult(`Email ${email} is ${Math.random() > 0.5 ? 'valid' : 'invalid'}`);
      setIsLoading(false);
    }, 1500);
  };

  return (
    
      <div className="space-y-6">
        {/* Page Header */}
        <div className="border-b border-neutral-200 pb-4 dark:border-neutral-700">
          <h1 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100">
            Email Verification
          </h1>
          <p className="mt-2 text-neutral-600 dark:text-neutral-400">
            Verify individual email addresses for deliverability and validity
          </p>
        </div>

        {/* Verification Form */}
        <div className="mx-auto max-w-2xl">
          <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm dark:border-neutral-700 dark:bg-neutral-800">
            <h2 className="mb-4 text-xl font-semibold text-neutral-800 dark:text-neutral-200">
              Single Email Verification
            </h2>
            
            <div className="space-y-4">
              <div>
                <label 
                  htmlFor="email" 
                  className="block text-sm font-medium text-neutral-700 dark:text-neutral-300"
                >
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter email address to verify"
                  className="mt-1 block w-full rounded-md border border-neutral-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-neutral-600 dark:bg-neutral-700 dark:text-neutral-100"
                />
              </div>
              
              <button
                onClick={handleVerifyEmail}
                disabled={!email || isLoading}
                className="w-full rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isLoading ? 'Verifying...' : 'Verify Email'}
              </button>
            </div>

            {/* Results */}
            {result && (
              <div className="mt-6 rounded-md border border-neutral-200 bg-neutral-50 p-4 dark:border-neutral-600 dark:bg-neutral-700">
                <h3 className="text-sm font-medium text-neutral-800 dark:text-neutral-200">
                  Verification Result
                </h3>
                <p className="mt-1 text-sm text-neutral-600 dark:text-neutral-400">
                  {result}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Additional Information */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="rounded-lg border border-neutral-200 bg-white p-6 dark:border-neutral-700 dark:bg-neutral-800">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-200">
              What We Check
            </h3>
            <ul className="mt-4 space-y-2 text-sm text-neutral-600 dark:text-neutral-400">
              <li>• Email format validation</li>
              <li>• Domain existence check</li>
              <li>• MX record verification</li>
              <li>• Mailbox existence (when possible)</li>
              <li>• Disposable email detection</li>
            </ul>
          </div>

          <div className="rounded-lg border border-neutral-200 bg-white p-6 dark:border-neutral-700 dark:bg-neutral-800">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-200">
              API Usage
            </h3>
            <div className="mt-4 space-y-2 text-sm text-neutral-600 dark:text-neutral-400">
              <div className="flex justify-between">
                <span>Verifications this month:</span>
                <span className="font-medium">1,234 / 10,000</span>
              </div>
              <div className="flex justify-between">
                <span>Remaining credits:</span>
                <span className="font-medium text-green-600">8,766</span>
              </div>
              <div className="mt-2 h-2 w-full rounded-full bg-neutral-200 dark:bg-neutral-600">
                <div className="h-2 w-1/8 rounded-full bg-blue-600"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
   
  );
}

export default EmailVerificationPage;
