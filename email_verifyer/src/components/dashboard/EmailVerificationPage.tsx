import React, { useState } from 'react';
import { Mail, Upload, CheckCircle, Users, Search, FileText, AlertCircle } from 'lucide-react';


type TabType = 'single' | 'bulk' | 'check';

const EmailValidationApp = () => {
  const [activeTab, setActiveTab] = useState<TabType>('single');
  const [singleEmail, setSingleEmail] = useState('');
  const [bulkEmails, setBulkEmails] = useState('');
  const [checkEmail, setCheckEmail] = useState('');

  const tabs = [
    { id: 'single', label: 'Single Validation', icon: Mail },
    { id: 'bulk', label: 'Bulk Validation', icon: Users },
    { id: 'check', label: 'Check Available', icon: Search }
  ] as const;

  const handleSingleValidate = () => {
    console.log('Validating single email:', singleEmail);
  };

  const handleBulkValidate = () => {
    console.log('Validating bulk emails');
  };

  const handleCheckAvailability = () => {
    console.log('Checking email availability:', checkEmail);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      console.log('File uploaded:', file.name);
    }
  };

  return (
   
      <div className="max-w-full mx-auto">
        {/* Content starts here - removed duplicate header since it's now in the layout */}

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm  border-gray-200 mb-6">
          <div className="flex border-b border-gray-200">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-6 py-4 font-medium text-sm transition-colors relative ${
                  activeTab === tab.id
                    ? 'text-green-600 border-b-2 border-green-600 bg-green-50'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
              >
                <tab.icon className="w-4 h-4 mr-2" />
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {/* Single Email Validation */}
            {activeTab === 'single' && (
              <div className="max-w-2xl mx-auto">
                <div className="text-center mb-6">
                  <Mail className="w-12 h-12 text-green-600 mx-auto mb-3" />
                  <h2 className="text-xl font-semibold text-gray-800 mb-2">Single Email Validation</h2>
                  <p className="text-gray-600">Validate individual email addresses for accuracy and deliverability</p>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={singleEmail}
                      onChange={(e) => setSingleEmail(e.target.value)}
                      placeholder="Enter email address to validate"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-all"
                    />
                  </div>

                  <button
                    onClick={handleSingleValidate}
                    disabled={!singleEmail}
                    className="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Validate Email
                  </button>
                </div>

                {/* Results Preview */}
                <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <h3 className="font-medium text-gray-800 mb-2">Validation Results</h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>Format:</span>
                      <span className="text-gray-400">Pending validation...</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Domain:</span>
                      <span className="text-gray-400">Pending validation...</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Deliverability:</span>
                      <span className="text-gray-400">Pending validation...</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Bulk Email Validation */}
            {activeTab === 'bulk' && (
              <div className="max-w-4xl mx-auto">
                <div className="text-center mb-6">
                  <Users className="w-12 h-12 text-green-600 mx-auto mb-3" />
                  <h2 className="text-xl font-semibold text-gray-800 mb-2">Bulk Email Validation</h2>
                  <p className="text-gray-600">Upload and validate multiple email addresses at once</p>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  {/* Upload Section */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-gray-800">Upload Email List</h3>
                    
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors">
                      <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600 mb-2">
                        Drop your CSV file here or click to browse
                      </p>
                      <input
                        type="file"
                        accept=".csv,.txt"
                        onChange={handleFileUpload}
                        className="hidden"
                        id="file-upload"
                      />
                      <label
                        htmlFor="file-upload"
                        className="inline-block bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 cursor-pointer transition-colors"
                      >
                        Choose File
                      </label>
                    </div>

                    <div className="text-xs text-gray-500">
                      <p>• Supported formats: CSV, TXT</p>
                      <p>• Maximum 10,000 emails per upload</p>
                      <p>• One email per line</p>
                    </div>
                  </div>

                  {/* Text Input Section */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-gray-800">Or Paste Email List</h3>
                    
                    <textarea
                      value={bulkEmails}
                      onChange={(e) => setBulkEmails(e.target.value)}
                      placeholder="Paste email addresses here (one per line)&#10;<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                      rows={8}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-all resize-none"
                    />
                    
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>{bulkEmails.split('\n').filter(line => line.trim()).length} emails</span>
                      <button className="text-green-600 hover:text-green-700 font-medium">
                        Clear All
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-center">
                  <button
                    onClick={handleBulkValidate}
                    className="bg-green-600 text-white py-3 px-8 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Validate All Emails
                  </button>
                </div>

                {/* Results Summary */}
                <div className="mt-8 grid sm:grid-cols-3 gap-4">
                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-green-600 font-medium">Valid</p>
                        <p className="text-2xl font-bold text-green-800">0</p>
                      </div>
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                  </div>
                  
                  <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-red-600 font-medium">Invalid</p>
                        <p className="text-2xl font-bold text-red-800">0</p>
                      </div>
                      <AlertCircle className="w-8 h-8 text-red-600" />
                    </div>
                  </div>
                  
                  <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-yellow-600 font-medium">Risky</p>
                        <p className="text-2xl font-bold text-yellow-800">0</p>
                      </div>
                      <AlertCircle className="w-8 h-8 text-yellow-600" />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Check Available Email */}
            {activeTab === 'check' && (
              <div className="max-w-2xl mx-auto">
                <div className="text-center mb-6">
                  <Search className="w-12 h-12 text-green-600 mx-auto mb-3" />
                  <h2 className="text-xl font-semibold text-gray-800 mb-2">Check Email Availability</h2>
                  <p className="text-gray-600">Verify if an email address is available or already in use</p>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address to Check
                    </label>
                    <input
                      type="email"
                      value={checkEmail}
                      onChange={(e) => setCheckEmail(e.target.value)}
                      placeholder="Enter email address to check availability"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-all"
                    />
                  </div>

                  <button
                    onClick={handleCheckAvailability}
                    disabled={!checkEmail}
                    className="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                  >
                    <Search className="w-4 h-4 mr-2" />
                    Check Availability
                  </button>
                </div>

                {/* Availability Results */}
                <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <h3 className="font-medium text-gray-800 mb-4">Availability Check Results</h3>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-white rounded border border-gray-200">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-gray-300 rounded-full mr-3"></div>
                        <span className="text-sm font-medium text-gray-700">Email Existence</span>
                      </div>
                      <span className="text-sm text-gray-400">Pending check...</span>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-white rounded border border-gray-200">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-gray-300 rounded-full mr-3"></div>
                        <span className="text-sm font-medium text-gray-700">Domain Status</span>
                      </div>
                      <span className="text-sm text-gray-400">Pending check...</span>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-white rounded border border-gray-200">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-gray-300 rounded-full mr-3"></div>
                        <span className="text-sm font-medium text-gray-700">Mailbox Status</span>
                      </div>
                      <span className="text-sm text-gray-400">Pending check...</span>
                    </div>
                  </div>
                </div>

                {/* Additional Info */}
                <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-start">
                    <AlertCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium mb-1">Important Note</p>
                      <p>This check verifies if an email address exists and can receive emails. It does not indicate whether the address is actively monitored or in use by a person.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>Secure, fast, and reliable email validation for your business needs</p>
        </div>
      </div>

  );
};

export default EmailValidationApp;