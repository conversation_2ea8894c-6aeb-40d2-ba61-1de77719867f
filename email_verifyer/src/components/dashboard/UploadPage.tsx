
import { Upload, FileText, Download } from 'lucide-react';

const UploadPage = () => {
  return (
      <div className="space-y-6">
        {/* Upload Section */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Upload Email List
          </h2>
          
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-green-400 transition-colors">
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Drop your files here
            </h3>
            <p className="text-gray-600 mb-4">
              or click to browse and upload
            </p>
            <button className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
              Choose Files
            </button>
          </div>
          
          <div className="mt-4 text-sm text-gray-500">
            <p>• Supported formats: CSV, TXT, XLSX</p>
            <p>• Maximum file size: 50MB</p>
            <p>• Maximum 100,000 emails per file</p>
          </div>
        </div>

        {/* Recent Files */}
        <div className="bg-white rounded-xl border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Files</h2>
          </div>
          
          <div className="p-6">
            <div className="space-y-4">
              {[
                { name: 'customer_emails_march.csv', size: '2.4 MB', status: 'Processed', date: '2024-03-15' },
                { name: 'newsletter_subscribers.csv', size: '1.8 MB', status: 'Processing', date: '2024-03-14' },
                { name: 'lead_generation_feb.csv', size: '3.2 MB', status: 'Completed', date: '2024-03-13' }
              ].map((file, index) => (
                <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <FileText className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900">{file.name}</p>
                      <p className="text-sm text-gray-500">{file.size} • {file.date}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      file.status === 'Completed' ? 'bg-green-100 text-green-800' :
                      file.status === 'Processing' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {file.status}
                    </span>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <Download className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
  );
};

export default UploadPage;
