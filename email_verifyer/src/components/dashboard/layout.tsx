"use client";
import { Sidebar, SidebarBody, SidebarLink } from "../ui/sidebar";
import {
  IconArrowLeft,
  IconBrandTabler,
  IconSettings,
  IconUserBolt,
  IconMail,
  IconShieldCheck,
  IconChartBar,
} from "@tabler/icons-react";
import { motion } from "motion/react";
import { useState } from "react";

interface DashboardLayoutProps {
  children?: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Sidebar navigation links
  const sidebarLinks = [
    {
      label: "Dashboard",
      href: "/dashboard",
      icon: (
        <IconBrandTabler className="h-5 w-5 shrink-0 text-neutral-700 dark:text-neutral-200" />
      ),
    },
    {
      label: "Email Verification",
      href: "/dashboard/verify",
      icon: (
        <IconMail className="h-5 w-5 shrink-0 text-neutral-700 dark:text-neutral-200" />
      ),
    },
    {
      label: "Bulk Verification",
      href: "/dashboard/bulk",
      icon: (
        <IconShieldCheck className="h-5 w-5 shrink-0 text-neutral-700 dark:text-neutral-200" />
      ),
    },
    {
      label: "Analytics",
      href: "/dashboard/analytics",
      icon: (
        <IconChartBar className="h-5 w-5 shrink-0 text-neutral-700 dark:text-neutral-200" />
      ),
    },
    {
      label: "Profile",
      href: "/dashboard/profile",
      icon: (
        <IconUserBolt className="h-5 w-5 shrink-0 text-neutral-700 dark:text-neutral-200" />
      ),
    },
    {
      label: "Settings",
      href: "/dashboard/settings",
      icon: (
        <IconSettings className="h-5 w-5 shrink-0 text-neutral-700 dark:text-neutral-200" />
      ),
    },
    {
      label: "Logout",
      href: "/logout",
      icon: (
        <IconArrowLeft className="h-5 w-5 shrink-0 text-neutral-700 dark:text-neutral-200" />
      ),
    },
  ];

  return (
    <div className="flex h-screen w-full flex-col overflow-hidden bg-gray-50 dark:bg-neutral-900">
    

      {/* Main Layout Container */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div className="shrink-0">
          <Sidebar open={sidebarOpen} setOpen={setSidebarOpen}>
            <SidebarBody className="justify-between gap-10">
              <div className="flex flex-1 flex-col overflow-x-hidden overflow-y-auto">
                {sidebarOpen ? <Logo /> : <LogoIcon />}
                <div className="mt-8 flex flex-col gap-2">
                  {sidebarLinks.map((link, idx) => (
                    <SidebarLink key={idx} link={link} />
                  ))}
                </div>
              </div>
              <div>
                <SidebarLink
                  link={{
                    label: "User Profile",
                    href: "/profile",
                    icon: (
                      <img
                        src="https://i.nextmedia.com.au/News/20220405115724_elon_musk.jpg"
                        className="h-7 w-7 shrink-0 rounded-full"
                        width={50}
                        height={50}
                        alt="Avatar"
                      />
                    ),
                  }}
                />
              </div>
            </SidebarBody>
          </Sidebar>
        </div>

        {/* Main Content Area */}
        <div className="flex flex-1 overflow-hidden">
          <div className="flex h-full w-full flex-1 flex-col gap-4 overflow-y-auto rounded-tl-2xl border border-neutral-200 bg-white p-4 md:p-8 dark:border-neutral-700 dark:bg-neutral-900">
            {children || <DefaultDashboardContent />}
          </div>
        </div>
      </div>
    </div>
  );
}

// Logo components for the sidebar
const Logo = () => {
  return (
    <a
      href="/dashboard"
      className="relative z-20 flex items-center space-x-2 px-2 py-1 text-sm font-normal text-black"
    >
      <div className="h-5 w-6 flex-shrink-0 rounded-bl-sm rounded-br-lg rounded-tl-lg rounded-tr-sm bg-black dark:bg-white" />
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="whitespace-pre font-medium text-black dark:text-white"
      >
        Email Verifier
      </motion.span>
    </a>
  );
};

const LogoIcon = () => {
  return (
    <a
      href="/dashboard"
      className="relative z-20 flex items-center space-x-2 px-2 py-1 text-sm font-normal text-black"
    >
      <div className="h-5 w-6 flex-shrink-0 rounded-bl-sm rounded-br-lg rounded-tl-lg rounded-tr-sm bg-black dark:bg-white" />
    </a>
  );
};

// Default dashboard content when no children are provided
const DefaultDashboardContent = () => {
  return (
    <div className="flex flex-1 flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-200">
          Email Verification Dashboard
        </h1>
        <p className="text-neutral-600 dark:text-neutral-400">
          Manage and verify email addresses with ease
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[
          { title: "Total Verifications", value: "1,234", change: "+12%" },
          { title: "Valid Emails", value: "987", change: "+8%" },
          { title: "Invalid Emails", value: "247", change: "-3%" },
          { title: "Success Rate", value: "80%", change: "+2%" },
        ].map((stat, idx) => (
          <div
            key={idx}
            className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm dark:border-neutral-700 dark:bg-neutral-800"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
                  {stat.value}
                </p>
              </div>
              <div className="text-sm font-medium text-green-600">
                {stat.change}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Area */}
      <div className="flex flex-1 gap-6">
        {/* Left Column */}
        <div className="flex-1 space-y-6">
          <div className="rounded-lg border border-neutral-200 bg-white p-6 dark:border-neutral-700 dark:bg-neutral-800">
            <h3 className="mb-4 text-lg font-semibold text-neutral-800 dark:text-neutral-200">
              Quick Actions
            </h3>
            <div className="space-y-3">
              <button className="w-full rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
                Verify Single Email
              </button>
              <button className="w-full rounded-md border border-neutral-300 px-4 py-2 text-neutral-700 hover:bg-neutral-50 dark:border-neutral-600 dark:text-neutral-300 dark:hover:bg-neutral-700">
                Upload CSV File
              </button>
              <button className="w-full rounded-md border border-neutral-300 px-4 py-2 text-neutral-700 hover:bg-neutral-50 dark:border-neutral-600 dark:text-neutral-300 dark:hover:bg-neutral-700">
                View Reports
              </button>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="flex-1 space-y-6">
          {/* <div className="rounded-lg border border-neutral-200 bg-white p-6 dark:border-neutral-700 dark:bg-neutral-800">
            <h3 className="mb-4 text-lg font-semibold text-neutral-800 dark:text-neutral-200">
              Recent Activity
            </h3>
            <div className="space-y-3">
              {[
                "Verified 50 emails from marketing list",
                "Bulk verification completed for client ABC",
                "API key regenerated",
                "Export completed: 1,000 verified emails",
              ].map((activity, idx) => (
                <div
                  key={idx}
                  className="flex items-center space-x-3 text-sm text-neutral-600 dark:text-neutral-400"
                >
                  <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                  <span>{activity}</span>
                </div>
              ))}
            </div>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;