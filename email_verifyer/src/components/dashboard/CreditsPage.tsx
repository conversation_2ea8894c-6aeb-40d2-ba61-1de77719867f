import React from 'react';
import { DashboardLayout } from './layout';
import { CreditCard, TrendingUp, Calendar, Package } from 'lucide-react';

const CreditsPage = () => {
  return (
    <DashboardLayout 
      title="Credits & Billing" 
      subtitle="Manage your credits and billing information"
      activeTab="credits"
    >
      <div className="space-y-6">
        {/* Credits Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Email Credits</p>
                <p className="text-2xl font-bold text-gray-900">47,350</p>
                <p className="text-sm text-green-600">+2,500 this month</p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <CreditCard className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Catchall Credits</p>
                <p className="text-2xl font-bold text-gray-900">1,200</p>
                <p className="text-sm text-blue-600">+100 this month</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Usage This Month</p>
                <p className="text-2xl font-bold text-gray-900">12,450</p>
                <p className="text-sm text-gray-600">25% of plan</p>
              </div>
              <div className="p-3 bg-gray-100 rounded-lg">
                <Calendar className="w-6 h-6 text-gray-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Buy Credits */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Purchase Additional Credits</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { credits: '10,000', price: '$49', popular: false },
              { credits: '25,000', price: '$99', popular: true },
              { credits: '50,000', price: '$179', popular: false }
            ].map((plan, index) => (
              <div key={index} className={`relative border rounded-lg p-6 ${
                plan.popular ? 'border-green-500 bg-green-50' : 'border-gray-200'
              }`}>
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <div className="text-center">
                  <Package className="w-8 h-8 text-gray-600 mx-auto mb-3" />
                  <h3 className="text-lg font-semibold text-gray-900">{plan.credits} Credits</h3>
                  <p className="text-2xl font-bold text-gray-900 mt-2">{plan.price}</p>
                  <p className="text-sm text-gray-600 mt-1">One-time purchase</p>
                  <button className={`w-full mt-4 py-2 px-4 rounded-lg font-medium transition-colors ${
                    plan.popular 
                      ? 'bg-green-600 text-white hover:bg-green-700' 
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                  }`}>
                    Purchase
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Usage History */}
        <div className="bg-white rounded-xl border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Usage History</h2>
          </div>
          
          <div className="p-6">
            <div className="space-y-4">
              {[
                { date: '2024-03-15', type: 'Email Validation', credits: -1250, remaining: 47350 },
                { date: '2024-03-14', type: 'Bulk Validation', credits: -2500, remaining: 48600 },
                { date: '2024-03-13', type: 'Credit Purchase', credits: +10000, remaining: 51100 },
                { date: '2024-03-12', type: 'Email Validation', credits: -850, remaining: 41100 }
              ].map((transaction, index) => (
                <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{transaction.type}</p>
                    <p className="text-sm text-gray-500">{transaction.date}</p>
                  </div>
                  <div className="text-right">
                    <p className={`font-medium ${
                      transaction.credits > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.credits > 0 ? '+' : ''}{transaction.credits.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-500">
                      Balance: {transaction.remaining.toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default CreditsPage;
